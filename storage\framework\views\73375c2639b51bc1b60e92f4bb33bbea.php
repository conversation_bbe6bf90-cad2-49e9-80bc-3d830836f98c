<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="bg-gray-100 min-h-screen py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">

            <!-- Header avec message de confirmation -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <div class="text-center">
                    <h1 class="text-3xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        Confirmation de participation
                    </h1>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <p class="text-lg text-gray-700">
                            Bonjour <span class="font-semibold text-green-600"><?php echo e($user->name); ?></span> !
                            Veuillez bien relire toutes les informations ci-dessous et confirmer si tout est correct.
                        </p>
                        <p class="text-base text-gray-600 mt-2">
                            Vous vous apprêtez à réserver
                            <span class="font-semibold"><?php echo e(session('n_tickets', 1)); ?> place(s)</span>
                            pour le covoiturage du
                            <span
                                class="font-semibold"><?php echo e(\Carbon\Carbon::parse($covoiturage->departure_date)->format('d/m/Y')); ?></span>
                            à <span
                                class="font-semibold"><?php echo e(\Carbon\Carbon::parse($covoiturage->departure_time)->format('H:i')); ?></span>,
                            de <span class="font-semibold"><?php echo e($covoiturage->departure_address); ?></span>
                            vers <span class="font-semibold"><?php echo e($covoiturage->arrival_address); ?></span>.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Contenu principal (reprise de la modale) -->
            <div class="bg-white rounded-lg shadow-lg p-6">

                <!-- Info du trajet -->
                <div class="trip-details-section mb-8">
                    <h4 class="text-xl font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">
                        <i class="fas fa-route mr-2 text-green-500"></i>Informations sur le trajet
                    </h4>

                    <!-- Route et dates -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="trip-details-departure flex flex-col items-center md:items-start">
                            <h5 class="font-semibold text-gray-700 mb-2">Départ</h5>
                            <div class="space-y-2">
                                <div class="flex items-center text-gray-600">
                                    <i class="fas fa-calendar mr-2 text-green-500"></i>
                                    <span><?php echo e(\Carbon\Carbon::parse($covoiturage->departure_date)->format('d/m/Y')); ?></span>
                                </div>
                                <div class="flex items-center text-gray-600">
                                    <i class="fas fa-clock mr-2 text-green-500"></i>
                                    <span><?php echo e(\Carbon\Carbon::parse($covoiturage->departure_time)->format('H:i')); ?></span>
                                </div>
                            </div>
                        </div>

                        <div class="trip-details-arrival flex flex-col items-center md:items-start">
                            <h5 class="font-semibold text-gray-700 mb-2">Arrivée</h5>
                            <div class="space-y-2">
                                <div class="flex items-center text-gray-600">
                                    <i class="fas fa-calendar mr-2 text-green-500"></i>
                                    <span><?php echo e(\Carbon\Carbon::parse($covoiturage->departure_date)->format('d/m/Y')); ?></span>
                                </div>
                                <div class="flex items-center text-gray-600">
                                    <i class="fas fa-clock mr-2 text-green-500"></i>
                                    <span><?php echo e(\Carbon\Carbon::parse($covoiturage->arrival_time)->format('H:i')); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Adresses -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="address-departure">
                            <h5 class="font-semibold text-gray-700 mb-2">Adresse de départ</h5>
                            <div class="flex items-start text-gray-600">
                                <i class="fas fa-map-marker-alt mr-2 text-green-500 mt-1"></i>
                                <span><?php echo e($covoiturage->departure_address); ?></span>
                            </div>
                        </div>

                        <div class="address-arrival">
                            <h5 class="font-semibold text-gray-700 mb-2">Adresse d'arrivée</h5>
                            <div class="flex items-start text-gray-600">
                                <i class="fas fa-map-marker-alt mr-2 text-green-500 mt-1"></i>
                                <span><?php echo e($covoiturage->arrival_address); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Prix et places -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="price-info text-center">
                            <h5 class="font-semibold text-gray-700 mb-2">Prix par place</h5>
                            <div class="text-2xl font-bold text-green-600">
                                <?php echo e($covoiturage->price); ?> crédits
                            </div>
                        </div>

                        <div class="seats-info text-center">
                            <h5 class="font-semibold text-gray-700 mb-2">Places disponibles</h5>
                            <div class="text-2xl font-bold text-blue-600">
                                <?php echo e($placesRestantes); ?>

                            </div>
                        </div>

                        <div class="duration-info text-center">
                            <h5 class="font-semibold text-gray-700 mb-2">Durée estimée</h5>
                            <div class="text-2xl font-bold text-purple-600">
                                <?php echo e($covoiturage->duration); ?>

                            </div>
                        </div>
                    </div>
                </div>

                <!-- Info du conducteur -->
                <div class="driver-details-section mb-8">
                    <h4 class="text-xl font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">
                        <i class="fas fa-user mr-2 text-green-500"></i>Informations sur le conducteur
                    </h4>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Photo et nom -->
                        <div class="driver-profile flex items-center space-x-4">
                            <?php if($conducteur->photo): ?>
                                <img src="data:<?php echo e($conducteur->phototype); ?>;base64,<?php echo e(base64_encode($conducteur->photo)); ?>"
                                    alt="Photo de <?php echo e($conducteur->name); ?>"
                                    class="w-16 h-16 rounded-full object-cover border-2 border-green-500">
                            <?php else: ?>
                                <div
                                    class="w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center border-2 border-green-500">
                                    <i class="fas fa-user text-gray-600 text-xl"></i>
                                </div>
                            <?php endif; ?>
                            <div>
                                <h5 class="font-semibold text-gray-800 text-lg"><?php echo e($conducteur->name); ?></h5>
                                <div class="flex items-center">
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <?php if($i <= floor($notesMoyenne)): ?>
                                            <i class="fas fa-star text-yellow-400"></i>
                                        <?php elseif($i - 0.5 <= $notesMoyenne): ?>
                                            <i class="fas fa-star-half-alt text-yellow-400"></i>
                                        <?php else: ?>
                                            <i class="far fa-star text-gray-300"></i>
                                        <?php endif; ?>
                                    <?php endfor; ?>
                                    <span class="ml-2 text-gray-600">(<?php echo e(number_format($notesMoyenne, 1)); ?>/5)</span>
                                </div>
                            </div>
                        </div>

                        <!-- Préférences -->
                        <div class="driver-preferences">
                            <h5 class="font-semibold text-gray-700 mb-2">Préférences</h5>
                            <div class="space-y-2">
                                <?php if($conducteur->pref_smoke): ?>
                                    <div class="flex items-center text-gray-600">
                                        <i class="fas fa-smoking mr-2 text-gray-500"></i>
                                        <span><?php echo e($conducteur->pref_smoke); ?></span>
                                    </div>
                                <?php endif; ?>
                                <?php if($conducteur->pref_pet): ?>
                                    <div class="flex items-center text-gray-600">
                                        <i class="fas fa-paw mr-2 text-gray-500"></i>
                                        <span>Animaux <?php echo e($conducteur->pref_pet); ?></span>
                                    </div>
                                <?php endif; ?>
                                <?php if($conducteur->pref_libre): ?>
                                    <div class="flex items-start text-gray-600">
                                        <i class="fas fa-comment mr-2 text-gray-500 mt-1"></i>
                                        <span><?php echo e($conducteur->pref_libre); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Info du véhicule -->
                <?php if($voiture): ?>
                    <div class="vehicle-details-section mb-8">
                        <h4 class="text-xl font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">
                            <i class="fas fa-car mr-2 text-green-500"></i>Informations sur le véhicule
                        </h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="vehicle-info">
                                <div class="space-y-3">
                                    <div class="flex items-center text-gray-600">
                                        <i class="fas fa-car mr-2 text-blue-500"></i>
                                        <span><strong>Marque :</strong> <?php echo e($voiture->marque); ?></span>
                                    </div>
                                    <div class="flex items-center text-gray-600">
                                        <i class="fas fa-cogs mr-2 text-blue-500"></i>
                                        <span><strong>Modèle :</strong> <?php echo e($voiture->modele); ?></span>
                                    </div>
                                    <div class="flex items-center text-gray-600">
                                        <i class="fas fa-palette mr-2 text-blue-500"></i>
                                        <span><strong>Couleur :</strong> <?php echo e($voiture->couleur); ?></span>
                                    </div>
                                    <div class="flex items-center text-gray-600">
                                        <i class="fas fa-leaf mr-2 text-green-500"></i>
                                        <span><strong>Énergie :</strong> <?php echo e($voiture->energie); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Avis des passagers -->
                <?php if($avis->count() > 0): ?>
                    <div class="reviews-section mb-8">
                        <h4 class="text-xl font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">
                            <i class="fas fa-comments mr-2 text-green-500"></i>Avis des passagers
                            (<?php echo e($avis->count()); ?>)
                        </h4>

                        <div class="space-y-4 max-h-64 overflow-y-auto">
                            <?php $__currentLoopData = $avis; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $avis_item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="review-card bg-gray-50 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="flex items-center">
                                            <?php for($i = 1; $i <= 5; $i++): ?>
                                                <?php if($i <= $avis_item->note): ?>
                                                    <i class="fas fa-star text-yellow-400"></i>
                                                <?php else: ?>
                                                    <i class="far fa-star text-gray-300"></i>
                                                <?php endif; ?>
                                            <?php endfor; ?>
                                            <span
                                                class="ml-2 font-semibold text-gray-700"><?php echo e($avis_item->note); ?>/5</span>
                                        </div>
                                        <span
                                            class="text-sm text-gray-500"><?php echo e(\Carbon\Carbon::parse($avis_item->date)->format('d/m/Y')); ?></span>
                                    </div>
                                    <?php if($avis_item->comment): ?>
                                        <p class="text-gray-600"><?php echo e($avis_item->comment); ?></p>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="reviews-section mb-8">
                        <h4 class="text-xl font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">
                            <i class="fas fa-comments mr-2 text-green-500"></i>Avis des passagers
                        </h4>
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-comment-slash text-4xl mb-4"></i>
                            <p>Aucun avis pour le moment</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Footer avec boutons -->
            <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?php echo e(route('covoiturage')); ?>"
                        class="px-8 py-3 bg-gray-600 hover:bg-gray-700 text-white font-bold rounded-lg transition-colors duration-300 text-center">
                        <i class="fas fa-times mr-2"></i>Annuler
                    </a>
                    <button id="confirm-participation-btn"
                        class="px-8 py-3 bg-green-600 hover:bg-green-700 text-white font-bold rounded-lg transition-colors duration-300">
                        <i class="fas fa-check mr-2"></i>Confirmer ma participation
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modale de paiement -->
    <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center hidden z-50"
        id="paymentModal">
        <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>

        <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded-lg shadow-xl z-50">
            <!-- Header -->
            <div class="modal-header flex justify-between items-center p-6 border-b border-gray-200">
                <h3 class="text-xl font-bold text-gray-800">Valider la transaction</h3>
                <button class="modal-close text-gray-400 hover:text-gray-600 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
            </div>

            <!-- Body -->
            <div class="modal-body p-6">
                <div class="space-y-4">
                    <!-- Crédits actuels -->
                    <div class="bg-blue-50 rounded-lg p-4">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-700">Vos crédits actuels :</span>
                            <span class="font-bold text-blue-600"><?php echo e($user->n_credit); ?> crédits</span>
                        </div>
                    </div>

                    <!-- Détails de la transaction -->
                    <div class="space-y-2">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-700">Prix par place :</span>
                            <span class="font-semibold"><?php echo e($covoiturage->price); ?> crédits</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-700">Nombre de places :</span>
                            <span class="font-semibold" id="payment-seats"><?php echo e(session('n_tickets', 1)); ?></span>
                        </div>
                        <hr class="my-2">
                        <div class="flex justify-between items-center text-lg">
                            <span class="text-gray-700">Coût total :</span>
                            <span class="font-bold text-red-600"
                                id="payment-total"><?php echo e($covoiturage->price * session('n_tickets', 1)); ?> crédits</span>
                        </div>
                    </div>

                    <!-- Crédits restants -->
                    <div class="bg-green-50 rounded-lg p-4">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-700">Crédits restants :</span>
                            <span class="font-bold text-green-600"
                                id="payment-remaining"><?php echo e($user->n_credit - $covoiturage->price * session('n_tickets', 1)); ?>

                                crédits</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="modal-footer flex justify-end space-x-4 p-6 border-t border-gray-200">
                <button
                    class="modal-close px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-bold rounded transition-colors duration-300">
                    Annuler
                </button>
                <a href="#"
                    class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-bold rounded transition-colors duration-300">
                    Valider la transaction
                </a>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const confirmBtn = document.getElementById('confirm-participation-btn');
            const paymentModal = document.getElementById('paymentModal');
            const closeButtons = paymentModal.querySelectorAll('.modal-close');

            // Ouvrir la modale de paiement
            confirmBtn.addEventListener('click', function() {
                paymentModal.classList.remove('hidden');
            });

            // Fermer la modale
            closeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    paymentModal.classList.add('hidden');
                });
            });

            // Fermer si on clique hors de la modale
            paymentModal.addEventListener('click', function(event) {
                if (event.target === paymentModal || event.target.classList.contains('modal-overlay')) {
                    paymentModal.classList.add('hidden');
                }
            });
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /var/www/html/resources/views/covoiturage-confirmation.blade.php ENDPATH**/ ?>